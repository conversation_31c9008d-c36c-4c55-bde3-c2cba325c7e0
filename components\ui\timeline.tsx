"use client";
import {
  useScroll,
  useTransform,
  motion,
 
} from "framer-motion";
import React, { useEffect, useRef, useState, } from "react";


interface TimelineEntry {
  title: string;
  content: React.ReactNode;
  date?: string;
  icon?: React.ReactNode;
  badge?: string;
  thumbnail?: string;
  color?: string;
}

export const Timeline = ({ data }: { data: TimelineEntry[] }) => {
  const ref = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState(0);

  useEffect(() => {
    if (ref.current) {
      const rect = ref.current.getBoundingClientRect();
      setHeight(rect.height);
    }
  }, [ref]);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start 10%", "end 50%"],
  });

  const heightTransform = useTransform(scrollYProgress, [0, 1], [0, height]);
  const opacityTransform = useTransform(scrollYProgress, [0, 0.1], [0, 1]);

  return (
    <div
      className="w-full bg-white dark:bg-black/[0.96] font-sans px-4 sm:px-6 md:px-8 lg:px-2"
      ref={containerRef}
    >
      {/* Header Section */}
      <div className="max-w-7xl mx-auto py-8 md:py-12 lg:py-16">
        <div className="text-center space-y-4">
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-b from-neutral-50 to-neutral-400">
            Experience
          </h2>
          <p className="text-neutral-700 dark:text-neutral-300 text-sm sm:text-base md:text-lg max-w-md mx-auto">
            My Professional Journey
          </p>
        </div>
      </div>

      <div ref={ref} className="relative max-w-7xl mx-auto">
        {data.map((item, index) => (
          <div
            key={index}
            className="flex items-start pt-6 sm:pt-8 md:pt-12 lg:pt-16 gap-4 sm:gap-6 md:gap-8"
          >
            {/* Timeline dot and line container */}
            <div className="flex flex-col items-center flex-shrink-0">
              <div className="h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12 rounded-full bg-white dark:bg-black flex items-center justify-center border-2 border-neutral-300 dark:border-neutral-700 z-10">
                <div className="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5 rounded-full bg-gradient-to-r from-purple-500 to-blue-500" />
              </div>
            </div>

            {/* Content container */}
            <div className="flex-1 min-w-0 pb-8 sm:pb-12 md:pb-16">
              <div className="space-y-2 sm:space-y-3 md:space-y-4">
                <h3 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-neutral-700 dark:text-neutral-300">
                  {item.title}
                </h3>
                <div className="text-sm sm:text-base md:text-lg text-neutral-600 dark:text-neutral-400">
                  {item.content}
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Vertical timeline line */}
        <div
          style={{
            height: height + "px",
          }}
          className="absolute left-4 sm:left-5 md:left-6 top-0 w-[2px] bg-gradient-to-b from-transparent from-[0%] via-neutral-200 dark:via-neutral-700 to-transparent to-[99%] [mask-image:linear-gradient(to_bottom,transparent_0%,black_10%,black_90%,transparent_100%)]"
        >
          <motion.div
            style={{
              height: heightTransform,
              opacity: opacityTransform,
            }}
            className="absolute inset-x-0 top-0 w-[2px] bg-gradient-to-t from-purple-500 via-blue-500 to-transparent from-[0%] via-[10%] rounded-full"
          />
        </div>
      </div>
    </div>
  );
};
