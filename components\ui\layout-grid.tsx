// "use client";
// import React, { useState } from "react";
// import { AnimatePresence, motion } from "framer-motion";
// import { cn } from "@/lib/utils";
// import Image from "next/image";
// import { X } from "lucide-react";
// import { GlowingEffect } from "@/components/ui/glowing-effect";

// type Card = {
//   id: number;
//   content: React.ReactElement | React.ReactNode | string;
//   className: string;
//   thumbnail: string;
// };

// export const LayoutGrid = ({ cards }: { cards: Card[] }) => {
//   const [selected, setSelected] = useState<Card | null>(null);
//   const [lastSelected, setLastSelected] = useState<Card | null>(null);

//   const handleClick = (card: Card) => {
//     setLastSelected(selected);
//     setSelected(card);
//   };

//   const handleOutsideClick = () => {
//     setLastSelected(selected);
//     setSelected(null);
//   };

//   return (
//     <div className="w-full h-full p-10 grid md:auto-rows-[180px] grid-cols-1 md:grid-cols-3 max-w-7xl mx-auto gap-4 relative">
//       {cards.map((card, i) => (
//         <div key={i} className={cn(card.className, "")}>
//           <motion.div
//             onClick={() => handleClick(card)}
//             className={cn(
//               card.className,
//               "relative overflow-hidden row-span-1 rounded-xl group/bento hover:shadow-xl transition duration-200 shadow-input dark:shadow-none dark:bg-black dark:border-white/[0.2] bg-white border border-transparent",
//               selected?.id === card.id
//                 ? "cursor-pointer absolute inset-0 h-1/2 w-full md:w-1/2 m-auto z-50 flex justify-center items-center flex-wrap flex-col"
//                 : lastSelected?.id === card.id
//                 ? "z-40 h-full w-full"
//                 : "h-full w-full"
//             )}
//             layoutId={`card-${card.id}`}
//           >
//             <GlowingEffect 
//               glow={true} 
//               disabled={false} 
//               blur={10}
//               spread={30}
//             />
//             {selected?.id === card.id && (
//               <SelectedCard selected={selected} onClose={handleOutsideClick} />
//             )}
//             <ImageComponent card={card} />
//           </motion.div>
//         </div>
//       ))}
//       <AnimatePresence>
//         {selected?.id && (
//           <motion.div
//             onClick={handleOutsideClick}
//             initial={{ opacity: 0 }}
//             animate={{ opacity: 0.3 }}
//             exit={{ opacity: 0 }}
//             className="absolute h-full w-full left-0 top-0 bg-black z-10"
//           />
//         )}
//       </AnimatePresence>
//     </div>
//   );
// };

// const ImageComponent = ({ card }: { card: Card }) => {
//   return (
//     <motion.img
//       layoutId={`image-${card.id}-image`}
//       src={card.thumbnail}
//       height="500"
//       width="500"
//       className="object-cover object-top absolute inset-0 h-full w-full transition duration-200"
//       alt="thumbnail"
//     />
//   );
// };

// const SelectedCard = ({ 
//   selected,
//   onClose 
// }: { 
//   selected: Card | null;
//   onClose: () => void;
// }) => {
//   return (
//     <div className="bg-transparent h-full w-full flex flex-col justify-end rounded-lg shadow-2xl relative z-[60]">
//       <motion.button
//         onClick={(e) => {
//           e.stopPropagation();
//           onClose();
//         }}
//         className="absolute top-4 right-4 z-[80] p-2 rounded-full bg-white/10 hover:bg-white/20 backdrop-blur-sm"
//         whileHover={{ scale: 1.1 }}
//         whileTap={{ scale: 0.95 }}
//       >
//         <X className="w-6 h-6 text-white" />
//       </motion.button>

//       <motion.div
//         initial={{ opacity: 0 }}
//         animate={{ opacity: 0.6 }}
//         className="absolute inset-0 h-full w-full bg-black opacity-60 z-10"
//       />
      
//       <motion.div
//         layoutId={`content-${selected?.id}`}
//         initial={{ opacity: 0, y: 100 }}
//         animate={{ opacity: 1, y: 0 }}
//         exit={{ opacity: 0, y: 100 }}
//         transition={{ duration: 0.3, ease: "easeInOut" }}
//         className="relative px-8 pb-4 z-[70] max-h-[80vh] overflow-y-auto custom-scrollbar"
//       >
//         {selected?.content}
//       </motion.div>
//     </div>
//   );
// };