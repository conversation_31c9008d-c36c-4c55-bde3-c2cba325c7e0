'use client';

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { IconType } from 'react-icons';
import { VscAzure } from "react-icons/vsc";
import { FaHtml5, FaCss3Alt, FaJs, FaReact, FaNodeJs, FaDocker, FaGithub, FaLinux, FaAws ,  } from 'react-icons/fa';
import { SiTypescript, SiNextdotjs, SiExpress, SiGraphql, SiMysql, SiMongodb, SiRedis, SiPostgresql, SiGooglecloud, SiTerraform, SiKubernetes,SiAnsible, SiGrafana, SiPrometheus } from 'react-icons/si';
import { TbApi } from 'react-icons/tb';

interface Tool {
  name: string;
  icon: IconType;
  color: string;
}

interface ToolCategory {
  title: string;
  description: string;
  tools: Tool[];
  header: string;
}

const tools: ToolCategory[] = [
  {
    title: "Frontend Development",
    description: "Building modern, responsive web applications",
    tools: [
      { name: "HTML5", icon: FaHtml5, color: "#E34F26" },
      { name: "CSS3", icon: FaCss3Alt, color: "#1572B6" },
      { name: "JavaScript", icon: FaJs, color: "#F7DF1E" },
      { name: "React", icon: FaReact, color: "#61DAFB" },
      { name: "Next.js", icon: SiNextdotjs, color: "#000000" }
    ],
    header: "🎨"
  },
  {
    title: "Backend Development",
    description: "Scalable server-side solutions",
    tools: [
      { name: "Node.js", icon: FaNodeJs, color: "#339933" },
      { name: "Express.js", icon: SiExpress, color: "#000000" },
      { name: "REST API", icon: TbApi, color: "#4285F4" },
      { name: "GraphQL", icon: SiGraphql, color: "#E535AB" }
    ],
    header: "⚙️"
  },
  {
    title: "Databases",
    description: "Data management and storage solutions",
    tools: [
      { name: "MySQL", icon: SiMysql, color: "#4479A1" },
      { name: "MongoDB", icon: SiMongodb, color: "#47A248" },
      { name: "Redis", icon: SiRedis, color: "#DC382D" },
      { name: "PostgreSQL", icon: SiPostgresql, color: "#336791" }
    ],
    header: "🗄️"
  },
  {
    title: "Cloud Services",
    description: "Cloud infrastructure and deployment",
    tools: [
      { name: "AWS", icon: FaAws, color: "#FF9900" },
      { name: "GCP", icon: SiGooglecloud, color: "#4285F4" },
      { name: "Azure", icon: VscAzure, color: "#0078D7" }, 
      { name : "Ansible", icon : SiAnsible, color : "#00A0B0" }
      ,{name : "Terraform", icon : SiTerraform, color : "#00A0B0" }
    ],
    header: "☁️"
  },
  {
    title: "CI/CD",
    description: "Continuous Integration and Deployment",
    tools: [
      { name: "GitHub Actions", icon: FaGithub, color: "#2088FF" }
    ],
    header: "🔄"
  },
  {
    title: "Containerization",
    description: "Container orchestration and management",
    tools: [
      { name: "Docker", icon: FaDocker, color: "#2496ED" },
      { name: "Kubernetes", icon: SiKubernetes, color: "#326CE5" }
    ],
    header: "📦"
  },
  {
    title: "Monitoring",
    description: "System monitoring and logging",
    tools: [
      { name: "Grafana", icon: SiGrafana, color: "#F46800" },
      { name: "Prometheus", icon: SiPrometheus, color: "#E6522C" }
    ],
    header: "📊"
  },
  {
    title: "Core Technologies",
    description: "Programming languages and systems",
    tools: [
      { name: "TypeScript", icon: SiTypescript, color: "#3178C6" },
      { name: "Linux", icon: FaLinux, color: "#FCC624" }
    ],
    header: "💻"
  }
];

interface ToolCardProps {
  tool: ToolCategory;
  className?: string;
}

const ToolCard = ({ tool, className }: ToolCardProps) => {
  return (
    <motion.div
      className={cn(
        "relative group rounded-xl overflow-hidden",
        className
      )}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <div className="relative z-10 p-6 bg-black/80 backdrop-blur-sm h-full">
        <div className="text-2xl mb-4">{tool.header}</div>
        <h3 className="text-xl font-bold mb-2 text-white">{tool.title}</h3>
        <p className="text-sm text-gray-300 mb-4">{tool.description}</p>

        <div className="flex flex-wrap gap-3">
          {tool.tools.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <motion.div
                key={index}
                className="flex items-center gap-2 bg-white/10 rounded-full px-4 py-2"
                whileHover={{ scale: 1.05, backgroundColor: item.color }}
                transition={{ duration: 0.2 }}
              >
                <IconComponent className="w-5 h-5" />
                <span className="text-sm">{item.name}</span>
              </motion.div>
            );
          })}
        </div>
      </div>
    </motion.div>
  );
};

export function TechStack() {
  return (
    <div className="min-h-screen w-full bg-black relative py-20" id="tools">
      <div className="absolute inset-0 bg-grid-white/[0.02]" />

      <div className="container mx-auto px-4 relative z-10">
        <motion.h2
          className="text-4xl md:text-7xl font-bold text-center bg-clip-text text-transparent bg-gradient-to-b from-white to-gray-500 pb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
        >
          Tech Stack
        </motion.h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
          {tools.map((tool, i) => (
            <ToolCard
              key={i}
              tool={tool}
              className={cn(
                "transform transition-transform duration-200",
                i % 2 === 0 ? "lg:translate-y-4" : ""
              )}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
