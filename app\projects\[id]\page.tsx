'use client';

import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import { projectsData, Project } from "@/components/Projects";

export default function ProjectDetails() {
  const params = useParams();
  const projectId = typeof params.id === 'string' ? params.id : params.id?.[0];
  const project = projectsData.find((p: Project) => p.id === projectId);

  if (!project) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <h1 className="text-2xl">Project not found</h1>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black">
      <div className="container mx-auto px-4 py-20">
        <Link
          href="/#projects"
          className="inline-block mb-8 text-sm hover:text-primary transition-colors"
        >
          ← Back to Projects
        </Link>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-12"
        >
          {/* MacBook Mockup Container */}
          <div className="relative perspective-1000">
            <div className="relative transform-gpu transition-transform duration-500 hover:rotateX-5 hover:rotateY-2">
              {/* MacBook Screen */}
              <div className="relative bg-gradient-to-b from-gray-800 via-gray-850 to-gray-900 rounded-t-xl p-6 shadow-2xl border border-gray-700/50">
                {/* Screen Bezel */}
                <div className="relative bg-black rounded-lg p-3 shadow-inner">
                  {/* Browser Window */}
                  <div className="bg-gray-800 rounded-t-lg">
                    {/* Browser Header */}
                    <div className="flex items-center gap-2 px-4 py-3 bg-gray-800 rounded-t-lg">
                      <div className="flex gap-1.5">
                        <div className="w-3 h-3 bg-red-500 rounded-full shadow-sm"></div>
                        <div className="w-3 h-3 bg-yellow-500 rounded-full shadow-sm"></div>
                        <div className="w-3 h-3 bg-green-500 rounded-full shadow-sm"></div>
                      </div>
                      <div className="flex-1 text-center">
                        <div className="bg-gray-700/80 backdrop-blur-sm rounded-md px-4 py-1.5 text-sm text-gray-300 inline-block border border-gray-600/50">
                          <span className="text-gray-500">🔒</span> {project.link.replace('https://', '').replace('http://', '')}
                        </div>
                      </div>
                    </div>

                    {/* Website Content */}
                    <div className="relative h-[300px] w-full overflow-hidden bg-white">
                      <Image
                        src={project.image}
                        alt={project.title}
                        fill
                        className="object-cover object-top"
                        priority
                        sizes="(max-width: 768px) 100vw, 50vw"
                      />
                      {/* Screen reflection */}
                      <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent pointer-events-none"></div>
                    </div>
                  </div>
                </div>

                {/* MacBook Camera */}
                <div className="absolute top-3 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 rounded-full border border-gray-600"></div>
              </div>

              {/* MacBook Keyboard Base */}
              <div className="relative bg-gradient-to-b from-gray-700 to-gray-800 rounded-b-xl px-8 py-4 shadow-lg">
                {/* Trackpad */}
                <div className="mx-auto w-24 h-16 bg-gray-600 rounded-lg border border-gray-500/50 shadow-inner"></div>

                {/* Keyboard suggestion */}
                <div className="absolute inset-x-6 top-2 h-1 bg-gradient-to-r from-transparent via-gray-600 to-transparent rounded-full opacity-50"></div>
              </div>

              {/* MacBook Shadow */}
              <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-full h-6 bg-black/20 rounded-full blur-lg scale-110"></div>
            </div>
          </div>

          <div>
            <h1 className="text-4xl font-bold mb-4">{project.title}</h1>
            <p className="text-gray-300 mb-8">{project.description}</p>

            <h2 className="text-xl font-semibold mb-4">Technologies Used</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
              {project.tech.map((tech, index) => (
                <div
                  key={index}
                  className="flex items-center gap-2 bg-white/10 rounded-lg p-3"
                >
                  <tech.icon className="w-6 h-6" />
                  <span className="text-sm">{tech.name}</span>
                </div>
              ))}
            </div>

            <a
              href={project.link}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block bg-primary text-black font-medium px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
            >
              Visit Project ↗
            </a>
          </div>
        </motion.div>
      </div>
    </div>
  );
}







