'use client';

import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import { projectsData, Project } from "@/components/Projects";

export default function ProjectDetails() {
  const params = useParams();
  const projectId = typeof params.id === 'string' ? params.id : params.id?.[0];
  const project = projectsData.find((p: Project) => p.id === projectId);

  if (!project) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <h1 className="text-2xl">Project not found</h1>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black">
      <div className="container mx-auto px-4 py-20">
        <Link
          href="/#projects"
          className="inline-block mb-8 text-sm hover:text-primary transition-colors"
        >
          ← Back to Projects
        </Link>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-12"
        >
          <div className="relative h-[400px] rounded-xl overflow-hidden">
            <Image
              src={project.image}
              alt={project.title}
              fill
              className="object-cover"
              priority
              sizes="(max-width: 768px) 100vw, 50vw"
            />
          </div>

          <div>
            <h1 className="text-4xl font-bold mb-4">{project.title}</h1>
            <p className="text-gray-300 mb-8">{project.description}</p>

            <h2 className="text-xl font-semibold mb-4">Technologies Used</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
              {project.tech.map((tech, index) => (
                <div
                  key={index}
                  className="flex items-center gap-2 bg-white/10 rounded-lg p-3"
                >
                  <tech.icon className="w-6 h-6" />
                  <span className="text-sm">{tech.name}</span>
                </div>
              ))}
            </div>

            <a
              href={project.link}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block bg-primary text-black font-medium px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
            >
              Visit Project ↗
            </a>
          </div>
        </motion.div>
      </div>
    </div>
  );
}







