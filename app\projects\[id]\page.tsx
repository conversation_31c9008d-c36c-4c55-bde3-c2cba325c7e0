'use client';

import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import { projectsData, Project } from "@/components/Projects";

export default function ProjectDetails() {
  const params = useParams();
  const projectId = typeof params.id === 'string' ? params.id : params.id?.[0];
  const project = projectsData.find((p: Project) => p.id === projectId);

  if (!project) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <h1 className="text-2xl">Project not found</h1>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black">
      <div className="container mx-auto px-4 py-20">
        <Link
          href="/#projects"
          className="inline-block mb-8 text-sm hover:text-primary transition-colors"
        >
          ← Back to Projects
        </Link>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-12"
        >
          {/* MacBook Pro Laptop Mockup */}
          <div className="relative mx-auto max-w-lg" style={{ perspective: '1200px' }}>
            <div
              className="relative transition-all duration-700 ease-out"
              style={{
                transformStyle: 'preserve-3d',
                transform: 'rotateX(-8deg) rotateY(-3deg)'
              }}
            >

              {/* MacBook Screen (Lid) */}
              <div
                className="relative bg-gradient-to-b from-gray-200 via-gray-300 to-gray-400 rounded-t-3xl shadow-2xl"
                style={{
                  transformOrigin: 'bottom center',
                  transform: 'rotateX(-18deg)',
                  transformStyle: 'preserve-3d'
                }}
              >
                {/* Screen Outer Frame */}
                <div className="p-3">
                  {/* Screen Bezel */}
                  <div className="bg-black rounded-2xl p-5 shadow-inner relative">
                    {/* Actual Screen */}
                    <div className="bg-gray-900 rounded-xl overflow-hidden relative">
                      {/* Browser Window */}
                      <div className="bg-gray-800 px-4 py-3 flex items-center gap-2">
                        <div className="flex gap-2">
                          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        </div>
                        <div className="flex-1 text-center">
                          <div className="bg-gray-700 rounded-md px-4 py-1.5 text-sm text-gray-300 inline-block max-w-[250px] truncate">
                            <span className="text-green-400">🔒</span> {project.link.replace('https://', '').replace('http://', '')}
                          </div>
                        </div>
                      </div>

                      {/* Website Content */}
                      <div className="relative h-64 w-full overflow-hidden bg-white">
                        <Image
                          src={project.image}
                          alt={project.title}
                          fill
                          className="object-cover object-top"
                          priority
                          sizes="(max-width: 768px) 100vw, 50vw"
                        />
                        {/* Screen reflection/glare */}
                        <div className="absolute inset-0 bg-gradient-to-br from-white/30 via-transparent to-black/10 pointer-events-none"></div>
                      </div>
                    </div>

                    {/* Camera */}
                    <div className="absolute top-3 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-800 rounded-full border border-gray-600"></div>

                    {/* Microphone */}
                    <div className="absolute top-3 left-1/2 transform -translate-x-1/2 translate-x-5 w-1 h-1 bg-gray-700 rounded-full"></div>
                  </div>
                </div>

                {/* Apple Logo (back of screen) */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 opacity-20">
                  <svg viewBox="0 0 24 24" fill="currentColor" className="w-full h-full text-gray-600">
                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                  </svg>
                </div>
              </div>

              {/* MacBook Base (Keyboard Area) */}
              <div className="relative bg-gradient-to-b from-gray-200 via-gray-300 to-gray-400 rounded-b-3xl px-8 py-5 shadow-xl">
                {/* Keyboard */}
                <div className="bg-gray-800 rounded-xl p-4 mb-4 shadow-inner">
                  {/* Function Keys Row */}
                  <div className="grid grid-cols-12 gap-1 mb-1.5">
                    {Array.from({ length: 12 }).map((_, i) => (
                      <div key={i} className="h-2.5 bg-gray-700 rounded-sm shadow-sm"></div>
                    ))}
                  </div>
                  {/* Number Row */}
                  <div className="grid grid-cols-13 gap-1 mb-1.5">
                    {Array.from({ length: 13 }).map((_, i) => (
                      <div key={i} className="h-3 bg-gray-700 rounded-sm shadow-sm"></div>
                    ))}
                  </div>
                  {/* QWERTY Row */}
                  <div className="grid grid-cols-12 gap-1 mb-1.5">
                    {Array.from({ length: 12 }).map((_, i) => (
                      <div key={i} className="h-3 bg-gray-700 rounded-sm shadow-sm"></div>
                    ))}
                  </div>
                  {/* ASDF Row */}
                  <div className="grid grid-cols-11 gap-1 mb-1.5">
                    {Array.from({ length: 11 }).map((_, i) => (
                      <div key={i} className="h-3 bg-gray-700 rounded-sm shadow-sm"></div>
                    ))}
                  </div>
                  {/* Bottom Row */}
                  <div className="grid grid-cols-8 gap-1">
                    {Array.from({ length: 8 }).map((_, i) => (
                      <div key={i} className={`h-3 bg-gray-700 rounded-sm shadow-sm ${i === 3 ? 'col-span-2' : ''}`}></div>
                    ))}
                  </div>
                </div>

                {/* Trackpad */}
                <div className="mx-auto w-24 h-16 bg-gray-600 rounded-xl border border-gray-500 shadow-inner relative">
                  <div className="absolute inset-1 bg-gradient-to-br from-gray-500 to-gray-700 rounded-lg"></div>
                </div>

                {/* Speaker Grilles */}
                <div className="absolute top-3 left-6 right-6 flex justify-between">
                  <div className="flex gap-0.5">
                    {Array.from({ length: 10 }).map((_, i) => (
                      <div key={i} className="w-0.5 h-1.5 bg-gray-600 rounded-full"></div>
                    ))}
                  </div>
                  <div className="flex gap-0.5">
                    {Array.from({ length: 10 }).map((_, i) => (
                      <div key={i} className="w-0.5 h-1.5 bg-gray-600 rounded-full"></div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Laptop Shadow */}
              <div
                className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 w-full h-10 bg-black/40 rounded-full blur-2xl"
                style={{ transform: 'rotateX(90deg) translateZ(-30px)' }}
              ></div>
            </div>
          </div>

          <div>
            <h1 className="text-4xl font-bold mb-4">{project.title}</h1>
            <p className="text-gray-300 mb-8">{project.description}</p>

            <h2 className="text-xl font-semibold mb-4">Technologies Used</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
              {project.tech.map((tech, index) => (
                <div
                  key={index}
                  className="flex items-center gap-2 bg-white/10 rounded-lg p-3"
                >
                  <tech.icon className="w-6 h-6" />
                  <span className="text-sm">{tech.name}</span>
                </div>
              ))}
            </div>

            <a
              href={project.link}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block bg-primary text-black font-medium px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
            >
              Visit Project ↗
            </a>
          </div>
        </motion.div>
      </div>
    </div>
  );
}







