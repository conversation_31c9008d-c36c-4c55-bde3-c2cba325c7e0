@tailwind base;
@tailwind components;
@tailwind utilities;

/* MacBook 3D Transform Utilities */
@layer utilities {
  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-gpu {
    transform: translate3d(0, 0, 0);
  }

  .group:hover .group-hover\:rotateX-5 {
    transform: rotateX(5deg) rotateY(2deg);
  }

  .group:hover .group-hover\:rotateY-2 {
    transform: rotateY(2deg);
  }

  .gray-850 {
    background-color: rgb(31, 41, 55);
  }
}

body {
  font-family: Arial, Helvetica, sans-serif;
}
/* globals.css */
::-webkit-scrollbar {
  width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  background: black;
 
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: black;
  border-radius: 5px;
  display: none;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: black;
}
/* Reusable styles for <h1> */
h1 {
  @apply text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-neutral-50 to-neutral-400;
}

/* Reusable styles for <p> */
p {
  @apply text-neutral-300 mt-2 text-lg;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 6% / 96%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}
