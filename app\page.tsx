"use client";

import { NaviBar } from "@/components/NaviBar";
import ContectMe from "@/components/ContectMe";
import { Hero } from "@/components/Hero";
import { Experience } from "@/components/Experience";
import { motion } from "framer-motion";
import Skills from "@/components/Skills";
import { LoadingIntro } from "@/components/LoadingIntro";
import { useState, useEffect } from "react";
// import { TestimonialsSection } from "@/components/TestimonialsSection";
import { TechStack } from "@/components/TechStack";
import { Projects } from "@/components/Projects";

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Hide loading after animation completes
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000); // Should be slightly longer than the LoadingIntro duration

    return () => clearTimeout(timer);
  }, []);

  // const testimonials = [
  //   {
  //     author: {
  //       name: "<PERSON>",
  //       role: "Senior Developer",
  //       avatar: "/avatars/john.jpg",
  //       company: {
  //         name: "Tech Corp"
  //       }
  //     },
  //     text: "Working with Usman was an incredible experience. His technical expertise and problem-solving abilities are outstanding."
  //   },
  //   {
  //     author: {
  //       name: "Jane Smith",
  //       role: "Project Manager",
  //       avatar: "/avatars/jane.jpg",
  //       company: {
  //         name: "Innovation Labs"
  //       }
  //     },
  //     text: "Usman's dedication to quality and attention to detail made him an invaluable asset to our team."
  //   },
  //   // Add more testimonials as needed
  // ];

  return (
    <div>
      <LoadingIntro />
      
      <div className={`transition-opacity duration-500 ${isLoading ? 'opacity-0' : 'opacity-100'}`}>
        {/* Navigation Bar */}
        <NaviBar />

        {/* Sections */}
        <motion.div
          id="hero"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
          className="min-h-screen"
        >
          <Hero />
        </motion.div>

        <motion.div
          id="experience"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="h-full relative"
        >
          <Experience />
        </motion.div>

        <motion.div
         
         initial={{ opacity: 0 }}
         whileInView={{ opacity: 1 }}
         viewport={{ once: true }}
         transition={{ duration: 1 }}
         className="h-full relative"
       >
         <Projects />
       </motion.div>
        <motion.div
          id="Skills"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="h-full relative"
        >
          <Skills />
          <TechStack/>
        </motion.div>

       

        {/* <motion.div
          id="testimonials"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="h-full relative"
        >
          <TestimonialsSection
            title="What People Say"
            description="Hear from the people I've worked with about their experiences and results."
            testimonials={testimonials}
          />
        </motion.div> */}

        <motion.div
          id="contact"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="h-full"
        >
          <ContectMe />

          <p className="mt-4 w-full text-center text-sm">© {new Date().getFullYear()} Usman Digital. All rights reserved.</p>
      
        </motion.div>
      </div>
    </div>
  );
}

