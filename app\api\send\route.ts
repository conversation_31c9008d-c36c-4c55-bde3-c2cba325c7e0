import { NextResponse } from "next/server";
import nodemailer from "nodemailer";

export async function POST(req: Request) {
  try {
    const { name, email, message } = await req.json();

    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.Email_Form,
        pass: process.env.Email_password,
      },
    });

    // Email to admin
    const adminMailOptions = {
      from: process.env.Email_Form,
      to: process.env.Email_Form,
      subject: `Contact Form Submission from ${name}`,
      text: `
        Name: ${name}
        Email: ${email}
        Message: ${message}
      `,
      html: `
        <h3>New Contact Form Submission</h3>
        <p><strong>Name:</strong> ${name}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Message:</strong> ${message}</p>
      `,
    };

    // Auto-reply email to user
    const userMailOptions = {
      from: process.env.Email_Form,
      to: email,
      subject: "Thank you for contacting <PERSON><PERSON>",
      text: `
        Dear ${name},

        Thank you for reaching out! I have received your message and will get back to you as soon as possible.

        For your reference, here's a copy of your message:
        "${message}"

        Best regards,
        <PERSON><PERSON>
        <PERSON>man Digital
      `,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Thank you for contacting Usman Digital</h2>
          
          <p>Dear ${name},</p>
          
          <p>Thank you for reaching out! I have received your message and will get back to you as soon as possible.</p>
          
          <div style="background-color: #f5f5f5; padding: 15px; margin: 20px 0; border-left: 4px solid #333;">
            <p style="margin: 0;"><strong>Your message:</strong></p>
            <p style="margin: 10px 0 0;">"${message}"</p>
          </div>
          
          <p>Best regards,<br>
          Usman<br>
          Usman Digital</p>
          
          <hr style="border: 1px solid #eee; margin: 20px 0;">
          
          <p style="color: #666; font-size: 12px;">This is an automated response. Please do not reply to this email.</p>
        </div>
      `,
    };

    // Send both emails
    await Promise.all([
      transporter.sendMail(adminMailOptions),
      transporter.sendMail(userMailOptions)
    ]);

    return NextResponse.json(
      { message: "Emails sent successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Failed to send email:", error);
    return NextResponse.json(
      { error: "Failed to send email" },
      { status: 500 }
    );
  }
}
