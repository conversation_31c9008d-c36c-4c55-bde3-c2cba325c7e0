'use client';

import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useState } from 'react';
// React Icons
import { IconType } from 'react-icons';
import { FaDocker, FaGithub, FaPython, FaPhp, FaLaravel, FaNodeJs } from 'react-icons/fa';
import {
  SiTypescript,
  SiNestjs,
  SiPostgresql,
  SiKubernetes,
  SiGrafana,
  SiPrometheus,
  SiGooglecloud,
  SiAmazonec2,
  SiAmazons3,
  SiAmazon,
  SiAwslambda,
  SiAmazoncloudwatch,
  SiAmazoniam,
  SiCloudflare,
  SiMysql
} from 'react-icons/si';
import { TbBrandNextjs } from 'react-icons/tb';

export interface Project {
  id: string;
  title: string;
  shortDescription: string;
  description: string;
  link: string;
  image: string;
  tech: Array<{
    icon: IconType;
    name: string;
  }>;
}

export const projectsData: Project[] = [
   
  

   {
    id: "erient",
    title: "Erient",
    shortDescription: " AI Healthcare Platform",
    description: "Architected and deployed comprehensive AWS infrastructure for healthcare data processing with HIPAA compliance. Implemented EKS, Lambda, RDS, VPC, SNS, CloudTrail, and CloudWatch, ensuring secure EHR integration and robust monitoring.",
    link: "https://ereint.com/",
    image: "/erient.jpg",
    tech: [
      { icon: SiAmazon, name: "AWS" },
      { icon: SiKubernetes, name: "EKS" },
      { icon: SiAwslambda, name: "Lambda" },
      { icon: SiAmazons3, name: "S3" },
      { icon: SiPostgresql, name: "RDS" },
      { icon: SiAmazoniam, name: "IAM" },
      { icon: SiAmazoncloudwatch, name: "CloudWatch" },
      { icon: SiAmazonec2, name: "VPC" },
      { icon: FaPython, name: "Python" },
      { icon: FaGithub, name: "GitHub Actions" }
    ]
  },
  
  {
    id: "aqua-realtors",
    title: "Aqua Realtors and Builders",
    shortDescription: "Real estate platform",
    description: "Created full stack website with modern design and functionality",
    link: "https://www.aquarealtors.org/",
    image: "/aqua.png",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" },
      { icon: SiTypescript, name: "TypeScript" },
      { icon: SiPostgresql, name: "Neon DB" },
      { icon: SiNestjs, name: "Prisma" },
      { icon: SiAmazons3, name: "S3" },
    ]
  },
  
  {
    id: "diastolix",
    title: "Diastolix",
    shortDescription: "Monitoring & health tracking",
    description: "Implemented infrastructure with containerization and comprehensive monitoring solutions.",
    link: "https://diastolix.com",
    image: "/diastolix.jpg",
    tech: [
      { icon: FaNodeJs, name: "Node.js" },
      { icon: FaDocker, name: "Docker" },
      { icon: SiPrometheus, name: "Prometheus" },
      { icon: SiGrafana, name: "Grafana" }
    ]
  },
   {
    id: "buyneons",
    title: "BuyNeons",
    shortDescription: "E-commerce Platform",
    description: "Created and implemented a full-featured e-commerce platform with modern design and robust backend infrastructure. Utilized AWS services for deployment and monitoring.",
    link: "https://www.buyneons.com/",
    image: "/buyneons.jpg",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" },
      { icon: FaNodeJs, name: "Node.js" },
      { icon: SiAmazon, name: "AWS" },
      { icon: SiKubernetes, name: "EKS" },
      { icon: SiPrometheus, name: "Prometheus" },
      { icon: SiGrafana, name: "Grafana" }
    ]
  },

  {
    id: "nobese",
    title: "Nobese",
    shortDescription: "Health & Wellness Platform",
    description: "Implemented infrastructure and collaborated with the development team to build a health and wellness platform. Set up Docker containers, Laravel environment, and GCP services including Cloud VM, Cloud SQL, and Cloud Storage. Integrated Cloudflare for security and performance optimization.",
    link: "https://nobese.com/",
    image: "/nobese.jpg",
    tech: [
      { icon: FaLaravel, name: "Laravel" },
      { icon: FaDocker, name: "Docker" },
      { icon: SiGooglecloud, name: "GCP" },
      { icon: SiMysql, name: "Cloud SQL" },
      { icon: SiGooglecloud, name: "Cloud Storage" },
      { icon: SiCloudflare, name: "Cloudflare" }
    ]
  },
  {
    id: "bettermood",
    title: "The Better Mood",
    shortDescription: "Mental Wellness Application",
    description: "Implemented infrastructure and collaborated with the development team for a mental wellness application. Configured Docker environments, PHP setup, and GCP services including Cloud VM, Cloud SQL, and Cloud Storage for optimal performance and scalability.",
    link: "https://www.thebettermood.com/",
    image: "/bettermood.jpg",
    tech: [
      { icon: FaPhp, name: "PHP" },
      { icon: FaDocker, name: "Docker" },
      { icon: SiGooglecloud, name: "GCP" },
      { icon: SiMysql, name: "Cloud SQL" },
      { icon: SiGooglecloud, name: "Cloud Storage" }
    ]
  }
  ,
 
 
  {
    id: "tranquely",
    title: "Tranquely",
    shortDescription: "stress management and relaxation app",
    description: "Collaborated on infrastructure, configurations and building APIs",
    link: "https://tranquely.com",
    image: "/tranquely.jpg",
    tech: [
      { icon: FaPhp, name: "PHP" },
      { icon: SiPostgresql, name: "SQL" },
      { icon: SiAmazonec2, name: "EC2" },
      { icon: FaGithub, name: "GitHub Actions" },
      { icon: SiGooglecloud, name: "GCP Bucket" },
    ]
  },
  {
    id: "peace",
    title: "Peace MD",
    shortDescription: "Healthcare Platform",
    description: "Developed and maintained a healthcare platform with secure patient data management and responsive user interface.",
    link: "https://peace.md/",
    image: "/peace.jpg",
    tech: [
      { icon: FaPhp, name: "PHP" },
      { icon: SiGooglecloud, name: "GCP" },
      { icon: SiGooglecloud, name: "Cloud Bucket" },
      { icon: SiMysql, name: "MySQL" }
    ]
  },
  {
    id: "editor",
    title: "Document Editor",
    shortDescription: "MDX, Email & PDF Editor",
    description: "Built a versatile document editor supporting MDX, email templates, and PDF generation with a clean, intuitive interface.",
    link: "https://editor.usman.digital",
    image: "/editor.jpg",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" }
    ]
  }
  ,
  {
    id: "ebook",
    title: "Sales Management",
    shortDescription: "Inventory Management System",
    description: "Developed a data record, sales and purchase inventory management system for tracking ebook sales and inventory.",
    link: "https://ebook.usman.digital/login",
    image: "/ebook.jpg",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" },
      { icon: SiMysql, name: "MySQL" }
    ]
  }
  ,
  {
    id: "bluesky",
    title: "BlueSky",
    shortDescription: "Blue Sky post downloader",
    description: "Developed a web application using Next.js with modern design and functionality.",
    link: "https://bluesky.usman.digital/",
    image: "/bluesky.jpg",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" }
    ]
  }
  ,
  {
    id: "ip-locator",
    title: "IP Locator",
    shortDescription: "IP & Location Tracker",
    description: "Created a web application that displays users' current location and IP address information using Next.js. The app provides geolocation data and network details in a clean, user-friendly interface.",
    link: "https://ip.usman.digital/",
    image: "/ip-locator.jpg",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" },
      { icon: SiTypescript, name: "TypeScript" }
    ]
  }
  ,
  {
    id: "tts",
    title: "Text to Speech",
    shortDescription: "Audio Conversion Tool",
    description: "Created a text-to-speech conversion application that transforms written content into natural-sounding audio.",
    link: "https://tts.usman.digital/",
    image: "/tts.jpg",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" }
    ]
  }
];

const ProjectCard = ({ project }: { project: Project }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 1 }}
      className="group relative rounded-lg overflow-hidden bg-black/50 backdrop-blur-sm p-8"
    >
      {/* MacBook Pro Laptop Mockup */}
      <div className="relative mx-auto max-w-md" style={{ perspective: '1200px' }}>
        <div
          className="relative transition-all duration-700 ease-out"
          style={{
            transformStyle: 'preserve-3d',
            transform: 'rotateX(-10deg) rotateY(-5deg)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'rotateX(-5deg) rotateY(5deg) scale(1.05)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'rotateX(-10deg) rotateY(-5deg) scale(1)';
          }}
        >

          {/* MacBook Screen (Lid) */}
          <div
            className="relative bg-gradient-to-b from-gray-200 via-gray-300 to-gray-400 rounded-t-3xl shadow-2xl"
            style={{
              transformOrigin: 'bottom center',
              transform: 'rotateX(-20deg)',
              transformStyle: 'preserve-3d'
            }}
          >
            {/* Screen Outer Frame */}
            <div className="p-2">
              {/* Screen Bezel */}
              <div className="bg-black rounded-2xl p-4 shadow-inner relative">
                {/* Actual Screen */}
                <div className="bg-gray-900 rounded-xl overflow-hidden relative">
                  {/* Browser Window */}
                  <div className="bg-gray-800 px-3 py-2 flex items-center gap-2">
                    <div className="flex gap-1.5">
                      <div className="w-3 h-3 bg-red-500 rounded-full hover:bg-red-400 transition-colors cursor-pointer"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-full hover:bg-yellow-400 transition-colors cursor-pointer"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-full hover:bg-green-400 transition-colors cursor-pointer"></div>
                    </div>
                    <div className="flex-1 text-center">
                      <div className="bg-gray-700 rounded-md px-3 py-1 text-xs text-gray-300 inline-block max-w-[180px] truncate">
                        <span className="text-green-400">🔒</span> {project.link.replace('https://', '').replace('http://', '')}
                      </div>
                    </div>
                  </div>

                  {/* Website Content */}
                  <div className="relative h-44 w-full overflow-hidden bg-white">
                    <div className="relative h-[1300px] w-full transition-transform duration-[18000ms] group-hover:translate-y-[-70%]">
                      <Image
                        src={project.image}
                        alt={project.title}
                        fill
                        className="object-cover object-top"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      />
                    </div>
                    {/* Screen reflection/glare */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white/30 via-transparent to-black/10 pointer-events-none"></div>
                  </div>
                </div>

                {/* Camera */}
                <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-800 rounded-full border border-gray-600"></div>

                {/* Microphone */}
                <div className="absolute top-2 left-1/2 transform -translate-x-1/2 translate-x-4 w-1 h-1 bg-gray-700 rounded-full"></div>
              </div>
            </div>

            {/* Apple Logo (back of screen) */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 opacity-20">
              <svg viewBox="0 0 24 24" fill="currentColor" className="w-full h-full text-gray-600">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
              </svg>
            </div>
          </div>

          {/* MacBook Base (Keyboard Area) */}
          <div className="relative bg-gradient-to-b from-gray-200 via-gray-300 to-gray-400 rounded-b-3xl px-6 py-4 shadow-xl">
            {/* Keyboard */}
            <div className="bg-gray-800 rounded-xl p-3 mb-3 shadow-inner">
              {/* Function Keys Row */}
              <div className="grid grid-cols-12 gap-1 mb-1">
                {Array.from({ length: 12 }).map((_, i) => (
                  <div key={i} className="h-2 bg-gray-700 rounded-sm shadow-sm"></div>
                ))}
              </div>
              {/* Number Row */}
              <div className="grid grid-cols-13 gap-1 mb-1">
                {Array.from({ length: 13 }).map((_, i) => (
                  <div key={i} className="h-2.5 bg-gray-700 rounded-sm shadow-sm"></div>
                ))}
              </div>
              {/* QWERTY Row */}
              <div className="grid grid-cols-12 gap-1 mb-1">
                {Array.from({ length: 12 }).map((_, i) => (
                  <div key={i} className="h-2.5 bg-gray-700 rounded-sm shadow-sm"></div>
                ))}
              </div>
              {/* ASDF Row */}
              <div className="grid grid-cols-11 gap-1 mb-1">
                {Array.from({ length: 11 }).map((_, i) => (
                  <div key={i} className="h-2.5 bg-gray-700 rounded-sm shadow-sm"></div>
                ))}
              </div>
              {/* Bottom Row */}
              <div className="grid grid-cols-8 gap-1">
                {Array.from({ length: 8 }).map((_, i) => (
                  <div key={i} className={`h-2.5 bg-gray-700 rounded-sm shadow-sm ${i === 3 ? 'col-span-2' : ''}`}></div>
                ))}
              </div>
            </div>

            {/* Trackpad */}
            <div className="mx-auto w-20 h-12 bg-gray-600 rounded-xl border border-gray-500 shadow-inner relative">
              <div className="absolute inset-1 bg-gradient-to-br from-gray-500 to-gray-700 rounded-lg"></div>
            </div>

            {/* Speaker Grilles */}
            <div className="absolute top-2 left-4 right-4 flex justify-between">
              <div className="flex gap-0.5">
                {Array.from({ length: 8 }).map((_, i) => (
                  <div key={i} className="w-0.5 h-1 bg-gray-600 rounded-full"></div>
                ))}
              </div>
              <div className="flex gap-0.5">
                {Array.from({ length: 8 }).map((_, i) => (
                  <div key={i} className="w-0.5 h-1 bg-gray-600 rounded-full"></div>
                ))}
              </div>
            </div>
          </div>

          {/* Laptop Shadow */}
          <div
            className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 w-full h-8 bg-black/40 rounded-full blur-2xl"
            style={{ transform: 'rotateX(90deg) translateZ(-20px)' }}
          ></div>
        </div>
      </div>


      <div className="p-6">
        <h3 className="text-xl font-bold mb-2">{project.title}</h3>
        <p className="text-gray-300 mb-4">{project.shortDescription}</p>

        <div className="flex flex-wrap gap-2 mb-4">
          {project.tech.slice(0, 4).map((tech, index) => {
            const Icon = tech.icon;
            return (
              <Icon
                key={index}
                className="w-6 h-6 text-gray-400 hover:text-white transition-colors"
                aria-label={tech.name}
              />
            );
          })}
          {project.tech.length > 4 && (
            <span className="text-sm text-gray-400">+{project.tech.length - 4} more</span>
          )}
        </div>

        <div className="flex gap-4">
          <Link
            href={`/projects/${project.id}`}
            className="text-sm font-medium hover:text-primary transition-colors"
          >
            View Details →
          </Link>
          <a
            href={project.link}
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm font-medium hover:text-primary transition-colors"
          >
            Visit Site ↗
          </a>
        </div>
      </div>
    </motion.div>
  );
};

export function Projects() {
  
  const [showAll, setShowAll] = useState(false);
  const displayedProjects = showAll ? projectsData : projectsData.slice(0, 3);

  return (
    <div className="min-h-screen w-full bg-black relative py-20" id="projects">
      <div className="absolute inset-0 bg-grid-white/[0.02]" />

      <div className="container mx-auto px-4 relative z-10">
        <motion.h2
          className="text-4xl md:text-7xl font-bold text-center bg-clip-text text-transparent bg-gradient-to-b from-white to-gray-500 pb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
        >
          Featured Projects
        </motion.h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
          {displayedProjects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>
        
        <div className="flex justify-center mt-12">
          <button
            onClick={() => setShowAll(!showAll)}
            className="px-6 py-3 bg-primary text-black font-medium rounded-lg hover:bg-primary/90 transition-colors"
          >
            {showAll ? "Show Less" : "Show All Projects"}
          </button>
        </div>
      </div>
    </div>
  );
}


