'use client';

import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useState } from 'react';
// React Icons
import { IconType } from 'react-icons';
import { FaDocker, FaGithub, FaPython, FaPhp, FaLaravel, FaNodeJs } from 'react-icons/fa';
import {
  SiTypescript,
  SiNestjs,
  SiPostgresql,
  SiKubernetes,
  SiGrafana,
  SiPrometheus,
  SiGooglecloud,
  SiAmazonec2,
  SiAmazons3,
  SiAmazon,
  SiAwslambda,
  SiAmazoncloudwatch,
  SiAmazoniam,
  SiCloudflare,
  SiMysql
} from 'react-icons/si';
import { TbBrandNextjs } from 'react-icons/tb';

export interface Project {
  id: string;
  title: string;
  shortDescription: string;
  description: string;
  link: string;
  image: string;
  tech: Array<{
    icon: IconType;
    name: string;
  }>;
}

export const projectsData: Project[] = [
   
  

   {
    id: "erient",
    title: "Erient",
    shortDescription: " AI Healthcare Platform",
    description: "Architected and deployed comprehensive AWS infrastructure for healthcare data processing with HIPAA compliance. Implemented EKS, Lambda, RDS, VPC, SNS, CloudTrail, and CloudWatch, ensuring secure EHR integration and robust monitoring.",
    link: "https://ereint.com/",
    image: "/erient.jpg",
    tech: [
      { icon: SiAmazon, name: "AWS" },
      { icon: SiKubernetes, name: "EKS" },
      { icon: SiAwslambda, name: "Lambda" },
      { icon: SiAmazons3, name: "S3" },
      { icon: SiPostgresql, name: "RDS" },
      { icon: SiAmazoniam, name: "IAM" },
      { icon: SiAmazoncloudwatch, name: "CloudWatch" },
      { icon: SiAmazonec2, name: "VPC" },
      { icon: FaPython, name: "Python" },
      { icon: FaGithub, name: "GitHub Actions" }
    ]
  },
  
  {
    id: "aqua-realtors",
    title: "Aqua Realtors and Builders",
    shortDescription: "Real estate platform",
    description: "Created full stack website with modern design and functionality",
    link: "https://www.aquarealtors.org/",
    image: "/aqua.png",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" },
      { icon: SiTypescript, name: "TypeScript" },
      { icon: SiPostgresql, name: "Neon DB" },
      { icon: SiNestjs, name: "Prisma" },
      { icon: SiAmazons3, name: "S3" },
    ]
  },
  
  {
    id: "diastolix",
    title: "Diastolix",
    shortDescription: "Monitoring & health tracking",
    description: "Implemented infrastructure with containerization and comprehensive monitoring solutions.",
    link: "https://diastolix.com",
    image: "/diastolix.jpg",
    tech: [
      { icon: FaNodeJs, name: "Node.js" },
      { icon: FaDocker, name: "Docker" },
      { icon: SiPrometheus, name: "Prometheus" },
      { icon: SiGrafana, name: "Grafana" }
    ]
  },
   {
    id: "buyneons",
    title: "BuyNeons",
    shortDescription: "E-commerce Platform",
    description: "Created and implemented a full-featured e-commerce platform with modern design and robust backend infrastructure. Utilized AWS services for deployment and monitoring.",
    link: "https://www.buyneons.com/",
    image: "/buyneons.jpg",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" },
      { icon: FaNodeJs, name: "Node.js" },
      { icon: SiAmazon, name: "AWS" },
      { icon: SiKubernetes, name: "EKS" },
      { icon: SiPrometheus, name: "Prometheus" },
      { icon: SiGrafana, name: "Grafana" }
    ]
  },

  {
    id: "nobese",
    title: "Nobese",
    shortDescription: "Health & Wellness Platform",
    description: "Implemented infrastructure and collaborated with the development team to build a health and wellness platform. Set up Docker containers, Laravel environment, and GCP services including Cloud VM, Cloud SQL, and Cloud Storage. Integrated Cloudflare for security and performance optimization.",
    link: "https://nobese.com/",
    image: "/nobese.jpg",
    tech: [
      { icon: FaLaravel, name: "Laravel" },
      { icon: FaDocker, name: "Docker" },
      { icon: SiGooglecloud, name: "GCP" },
      { icon: SiMysql, name: "Cloud SQL" },
      { icon: SiGooglecloud, name: "Cloud Storage" },
      { icon: SiCloudflare, name: "Cloudflare" }
    ]
  },
  {
    id: "bettermood",
    title: "The Better Mood",
    shortDescription: "Mental Wellness Application",
    description: "Implemented infrastructure and collaborated with the development team for a mental wellness application. Configured Docker environments, PHP setup, and GCP services including Cloud VM, Cloud SQL, and Cloud Storage for optimal performance and scalability.",
    link: "https://www.thebettermood.com/",
    image: "/bettermood.jpg",
    tech: [
      { icon: FaPhp, name: "PHP" },
      { icon: FaDocker, name: "Docker" },
      { icon: SiGooglecloud, name: "GCP" },
      { icon: SiMysql, name: "Cloud SQL" },
      { icon: SiGooglecloud, name: "Cloud Storage" }
    ]
  }
  ,
 
 
  {
    id: "tranquely",
    title: "Tranquely",
    shortDescription: "stress management and relaxation app",
    description: "Collaborated on infrastructure, configurations and building APIs",
    link: "https://tranquely.com",
    image: "/tranquely.jpg",
    tech: [
      { icon: FaPhp, name: "PHP" },
      { icon: SiPostgresql, name: "SQL" },
      { icon: SiAmazonec2, name: "EC2" },
      { icon: FaGithub, name: "GitHub Actions" },
      { icon: SiGooglecloud, name: "GCP Bucket" },
    ]
  },
  {
    id: "peace",
    title: "Peace MD",
    shortDescription: "Healthcare Platform",
    description: "Developed and maintained a healthcare platform with secure patient data management and responsive user interface.",
    link: "https://peace.md/",
    image: "/peace.jpg",
    tech: [
      { icon: FaPhp, name: "PHP" },
      { icon: SiGooglecloud, name: "GCP" },
      { icon: SiGooglecloud, name: "Cloud Bucket" },
      { icon: SiMysql, name: "MySQL" }
    ]
  },
  {
    id: "editor",
    title: "Document Editor",
    shortDescription: "MDX, Email & PDF Editor",
    description: "Built a versatile document editor supporting MDX, email templates, and PDF generation with a clean, intuitive interface.",
    link: "https://editor.usman.digital",
    image: "/editor.jpg",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" }
    ]
  }
  ,
  {
    id: "ebook",
    title: "Sales Management",
    shortDescription: "Inventory Management System",
    description: "Developed a data record, sales and purchase inventory management system for tracking ebook sales and inventory.",
    link: "https://ebook.usman.digital/login",
    image: "/ebook.jpg",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" },
      { icon: SiMysql, name: "MySQL" }
    ]
  }
  ,
  {
    id: "bluesky",
    title: "BlueSky",
    shortDescription: "Blue Sky post downloader",
    description: "Developed a web application using Next.js with modern design and functionality.",
    link: "https://bluesky.usman.digital/",
    image: "/bluesky.jpg",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" }
    ]
  }
  ,
  {
    id: "ip-locator",
    title: "IP Locator",
    shortDescription: "IP & Location Tracker",
    description: "Created a web application that displays users' current location and IP address information using Next.js. The app provides geolocation data and network details in a clean, user-friendly interface.",
    link: "https://ip.usman.digital/",
    image: "/ip-locator.jpg",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" },
      { icon: SiTypescript, name: "TypeScript" }
    ]
  }
  ,
  {
    id: "tts",
    title: "Text to Speech",
    shortDescription: "Audio Conversion Tool",
    description: "Created a text-to-speech conversion application that transforms written content into natural-sounding audio.",
    link: "https://tts.usman.digital/",
    image: "/tts.jpg",
    tech: [
      { icon: TbBrandNextjs, name: "Next.js" }
    ]
  }
];

const ProjectCard = ({ project }: { project: Project }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 1 }}
      className="group relative rounded-lg overflow-hidden bg-black/50 backdrop-blur-sm"
    >
      {/* Mac Mockup Container */}
      <div className="relative bg-gradient-to-b from-gray-800 to-gray-900 rounded-t-lg p-3 shadow-2xl">
        {/* Mac Window Header */}
        <div className="flex items-center gap-2 mb-3">
          <div className="flex gap-1.5">
            <div className="w-3 h-3 bg-red-500 rounded-full shadow-sm hover:bg-red-400 transition-colors cursor-pointer"></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-full shadow-sm hover:bg-yellow-400 transition-colors cursor-pointer"></div>
            <div className="w-3 h-3 bg-green-500 rounded-full shadow-sm hover:bg-green-400 transition-colors cursor-pointer"></div>
          </div>
          <div className="flex-1 text-center">
            <div className="bg-gray-700/80 backdrop-blur-sm rounded-md px-3 py-1 text-xs text-gray-300 inline-block border border-gray-600/50">
              <span className="text-gray-500">🔒</span> {project.link.replace('https://', '').replace('http://', '')}
            </div>
          </div>
          <div className="w-12"></div> {/* Spacer for balance */}
        </div>

        {/* Mac Screen Content */}
        <div className="relative h-52 w-full overflow-hidden rounded-md bg-white shadow-inner border border-gray-700/30">
          <div className="relative h-[1500px] w-full transition-transform duration-[20000ms] group-hover:translate-y-[-70%]">
            <Image
              src={project.image}
              alt={project.title}
              fill
              className="object-cover object-top"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>
          {/* Subtle overlay for depth */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/5 to-transparent pointer-events-none"></div>
        </div>

        {/* Mac Base/Stand */}
        <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-2 bg-gradient-to-b from-gray-700 to-gray-800 rounded-b-lg"></div>
        <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gray-900 rounded-full"></div>
      </div>


      <div className="p-6">
        <h3 className="text-xl font-bold mb-2">{project.title}</h3>
        <p className="text-gray-300 mb-4">{project.shortDescription}</p>

        <div className="flex flex-wrap gap-2 mb-4">
          {project.tech.slice(0, 4).map((tech, index) => {
            const Icon = tech.icon;
            return (
              <Icon
                key={index}
                className="w-6 h-6 text-gray-400 hover:text-white transition-colors"
                aria-label={tech.name}
              />
            );
          })}
          {project.tech.length > 4 && (
            <span className="text-sm text-gray-400">+{project.tech.length - 4} more</span>
          )}
        </div>

        <div className="flex gap-4">
          <Link
            href={`/projects/${project.id}`}
            className="text-sm font-medium hover:text-primary transition-colors"
          >
            View Details →
          </Link>
          <a
            href={project.link}
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm font-medium hover:text-primary transition-colors"
          >
            Visit Site ↗
          </a>
        </div>
      </div>
    </motion.div>
  );
};

export function Projects() {
  
  const [showAll, setShowAll] = useState(false);
  const displayedProjects = showAll ? projectsData : projectsData.slice(0, 3);

  return (
    <div className="min-h-screen w-full bg-black relative py-20" id="projects">
      <div className="absolute inset-0 bg-grid-white/[0.02]" />

      <div className="container mx-auto px-4 relative z-10">
        <motion.h2
          className="text-4xl md:text-7xl font-bold text-center bg-clip-text text-transparent bg-gradient-to-b from-white to-gray-500 pb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
        >
          Featured Projects
        </motion.h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
          {displayedProjects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>
        
        <div className="flex justify-center mt-12">
          <button
            onClick={() => setShowAll(!showAll)}
            className="px-6 py-3 bg-primary text-black font-medium rounded-lg hover:bg-primary/90 transition-colors"
          >
            {showAll ? "Show Less" : "Show All Projects"}
          </button>
        </div>
      </div>
    </div>
  );
}


