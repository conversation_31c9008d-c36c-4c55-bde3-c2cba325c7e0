'use client';

import Image from "next/image";
import { ReactNode } from "react";

interface MacBookMockupProps {
  children?: ReactNode;
  imageUrl?: string;
  imageAlt?: string;
  websiteUrl?: string;
  className?: string;
  showScrollEffect?: boolean;
}

export function MacBookMockup({
  children,
  imageUrl,
  imageAlt = "Website preview",
  websiteUrl = "",
  className = "",
  showScrollEffect = true
}: MacBookMockupProps) {
  return (
    <div className={`relative group ${className}`}>
      {/* MacBook Container - 2D Design */}
      <div className="relative mx-auto max-w-md transition-all duration-500 ease-out group-hover:scale-105">
        <div className="relative bg-gradient-to-b from-gray-200 via-gray-300 to-gray-400 rounded-2xl shadow-2xl p-1 border border-gray-300">
          {/* MacBook Screen */}
          <div className="relative bg-black rounded-t-2xl p-3 shadow-inner">
            {/* Screen Bezel */}
            <div className="bg-gray-900 rounded-xl overflow-hidden relative">
              {/* Browser Window */}
              <div className="bg-gray-800 px-3 py-2 flex items-center gap-2">
                <div className="flex gap-1.5">
                  <div className="w-3 h-3 bg-red-500 rounded-full hover:bg-red-400 transition-colors cursor-pointer shadow-sm"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full hover:bg-yellow-400 transition-colors cursor-pointer shadow-sm"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full hover:bg-green-400 transition-colors cursor-pointer shadow-sm"></div>
                </div>
                <div className="flex-1 text-center">
                  <div className="bg-gray-700 rounded-md px-3 py-1 text-xs text-gray-300 inline-block max-w-[180px] truncate border border-gray-600">
                    <span className="text-green-400">🔒</span> {websiteUrl.replace('https://', '').replace('http://', '')}
                  </div>
                </div>
              </div>

              {/* Website Content */}
              <div className="relative h-44 w-full overflow-hidden bg-white">
                {imageUrl ? (
                  <div className={`relative h-[1300px] w-full transition-transform duration-[18000ms] ${showScrollEffect ? 'group-hover:translate-y-[-70%]' : ''}`}>
                    <Image
                      src={imageUrl}
                      alt={imageAlt}
                      fill
                      className="object-cover object-top"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  </div>
                ) : (
                  <div className="h-full w-full flex items-center justify-center bg-gray-100">
                    {children || (
                      <div className="text-gray-500 text-sm">No content</div>
                    )}
                  </div>
                )}
                {/* Screen reflection/glare */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-black/5 pointer-events-none"></div>
              </div>
            </div>

            {/* Camera */}
            <div className="absolute top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-800 rounded-full border border-gray-600 shadow-inner"></div>
          </div>
          
          {/* MacBook Base (Keyboard Area) */}
          <div className="relative bg-gradient-to-b from-gray-300 to-gray-400 rounded-b-2xl px-4 py-3 shadow-lg">
            {/* Keyboard */}
            <div className="bg-gray-800 rounded-lg p-2 mb-2 shadow-inner">
              {/* Simplified Keyboard Rows */}
              <div className="space-y-1">
                {/* Function Keys */}
                <div className="grid grid-cols-12 gap-0.5">
                  {Array.from({ length: 12 }).map((_, i) => (
                    <div key={i} className="h-1.5 bg-gray-700 rounded-sm"></div>
                  ))}
                </div>
                {/* Main Keys */}
                <div className="grid grid-cols-13 gap-0.5">
                  {Array.from({ length: 13 }).map((_, i) => (
                    <div key={i} className="h-2 bg-gray-700 rounded-sm"></div>
                  ))}
                </div>
                <div className="grid grid-cols-12 gap-0.5">
                  {Array.from({ length: 12 }).map((_, i) => (
                    <div key={i} className="h-2 bg-gray-700 rounded-sm"></div>
                  ))}
                </div>
                <div className="grid grid-cols-11 gap-0.5">
                  {Array.from({ length: 11 }).map((_, i) => (
                    <div key={i} className="h-2 bg-gray-700 rounded-sm"></div>
                  ))}
                </div>
                {/* Space bar row */}
                <div className="grid grid-cols-7 gap-0.5">
                  <div className="h-2 bg-gray-700 rounded-sm"></div>
                  <div className="h-2 bg-gray-700 rounded-sm"></div>
                  <div className="h-2 bg-gray-700 rounded-sm col-span-3"></div>
                  <div className="h-2 bg-gray-700 rounded-sm"></div>
                  <div className="h-2 bg-gray-700 rounded-sm"></div>
                </div>
              </div>
            </div>

            {/* Trackpad */}
            <div className="mx-auto w-16 h-10 bg-gray-600 rounded-lg border border-gray-500 shadow-inner"></div>

            {/* Apple Logo */}
            <div className="absolute bottom-1 right-3 w-3 h-3 opacity-30">
              <svg viewBox="0 0 24 24" fill="currentColor" className="w-full h-full text-gray-600">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
              </svg>
            </div>
          </div>

          {/* Simple Shadow */}
          <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-4/5 h-4 bg-black/30 rounded-full blur-lg"></div>
        </div>
      </div>
    </div>
  );
}
