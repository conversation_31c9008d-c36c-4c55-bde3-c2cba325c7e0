'use client';

import Image from "next/image";
import { ReactNode } from "react";

interface MacBookMockupProps {
  children?: ReactNode;
  imageUrl?: string;
  imageAlt?: string;
  websiteUrl?: string;
  className?: string;
  showScrollEffect?: boolean;
}

export function MacBookMockup({
  children,
  imageUrl,
  imageAlt = "Website preview",
  websiteUrl = "",
  className = "",
  showScrollEffect = true
}: MacBookMockupProps) {
  return (
    <div className={`relative group ${className}`}>
      {/* Laptop SVG Mockup using existing laptop.svg */}
      <div className="relative mx-auto max-w-xl transition-all duration-500 ease-out group-hover:scale-105">
        {/* Laptop SVG Background */}
        <img
          src="/laptop.svg"
          alt="Laptop mockup"
          className="w-full h-auto scale-110"
          style={{
            filter: 'drop-shadow(0 10px 25px rgba(0,0,0,0.3))',
            transform: 'scale(1.1)'
          }}
        />

        {/* Content Overlay */}
        <div className="absolute inset-0">
          {/* Browser Window Header */}
          <div
            className="absolute flex items-center gap-1 px-2"
            style={{
              left: '16.8%',
              top: '28.2%',
              width: '66.4%',
              height: '4%'
            }}
          >
            {/* Browser Buttons */}
            <div className="flex gap-1">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            </div>

            {/* URL Bar */}
            <div className="flex-1 mx-2">
              <div className="bg-gray-200 rounded px-2 py-0.5 text-xs text-gray-600 text-center">
                <span className="truncate text-[8px]">
                  🔒 {websiteUrl.replace('https://', '').replace('http://', '')}
                </span>
              </div>
            </div>
          </div>

          {/* Screen Content Area - positioned over the white rectangle in the SVG */}
          <div
            className="absolute overflow-hidden bg-white"
            style={{
              left: '16.8%',
              top: '32.2%',
              width: '66.4%',
              height: '37.3%'
            }}
          >
            {imageUrl ? (
              <div className={`relative h-[1000px] w-full transition-transform duration-[15000ms] ${showScrollEffect ? 'group-hover:translate-y-[-65%]' : ''}`}>
                <Image
                  src={imageUrl}
                  alt={imageAlt}
                  fill
                  className="object-cover object-top"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
            ) : (
              <div className="h-full w-full flex items-center justify-center bg-gray-100">
                {children || (
                  <div className="text-gray-500 text-xs">No content</div>
                )}
              </div>
            )}
            {/* Screen reflection */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/30 via-transparent to-black/10 pointer-events-none"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
