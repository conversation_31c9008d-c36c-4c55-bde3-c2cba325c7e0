'use client';

import Image from "next/image";
import { ReactNode } from "react";

interface MacBookMockupProps {
  children?: ReactNode;
  imageUrl?: string;
  imageAlt?: string;
  websiteUrl?: string;
  className?: string;
  showScrollEffect?: boolean;
}

export function MacBookMockup({
  children,
  imageUrl,
  imageAlt = "Website preview",
  websiteUrl = "",
  className = "",
  showScrollEffect = true
}: MacBookMockupProps) {
  return (
    <div className={`relative group ${className}`}>
      {/* Laptop SVG Mockup */}
      <div className="relative mx-auto max-w-md transition-all duration-500 ease-out group-hover:scale-105">
        <svg
          viewBox="0 0 400 300"
          className="w-full h-auto"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Laptop Base */}
          <defs>
            <linearGradient id="laptopGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#e5e7eb" />
              <stop offset="50%" stopColor="#d1d5db" />
              <stop offset="100%" stopColor="#9ca3af" />
            </linearGradient>
            <linearGradient id="screenGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#1f2937" />
              <stop offset="100%" stopColor="#111827" />
            </linearGradient>
          </defs>

          {/* Laptop Screen */}
          <rect
            x="50"
            y="20"
            width="300"
            height="180"
            rx="8"
            ry="8"
            fill="url(#laptopGradient)"
            stroke="#9ca3af"
            strokeWidth="1"
          />

          {/* Screen Bezel */}
          <rect
            x="60"
            y="30"
            width="280"
            height="160"
            rx="4"
            ry="4"
            fill="#000000"
          />

          {/* Browser Window */}
          <rect
            x="65"
            y="35"
            width="270"
            height="20"
            rx="2"
            ry="2"
            fill="#374151"
          />

          {/* Browser Buttons */}
          <circle cx="75" cy="45" r="3" fill="#ef4444" />
          <circle cx="85" cy="45" r="3" fill="#f59e0b" />
          <circle cx="95" cy="45" r="3" fill="#10b981" />

          {/* URL Bar */}
          <rect
            x="110"
            y="40"
            width="120"
            height="10"
            rx="5"
            ry="5"
            fill="#4b5563"
          />

          {/* Screen Content Area */}
          <rect
            x="65"
            y="55"
            width="270"
            height="130"
            rx="2"
            ry="2"
            fill="#ffffff"
          />

          {/* Laptop Base/Keyboard */}
          <ellipse
            cx="200"
            cy="220"
            rx="180"
            ry="60"
            fill="url(#laptopGradient)"
            stroke="#9ca3af"
            strokeWidth="1"
          />

          {/* Keyboard Area */}
          <rect
            x="80"
            y="200"
            width="240"
            height="40"
            rx="4"
            ry="4"
            fill="#1f2937"
            opacity="0.8"
          />

          {/* Trackpad */}
          <rect
            x="170"
            y="250"
            width="60"
            height="40"
            rx="4"
            ry="4"
            fill="#4b5563"
            stroke="#6b7280"
            strokeWidth="1"
          />

          {/* Camera */}
          <circle cx="200" cy="25" r="2" fill="#374151" />
        </svg>

        {/* Content Overlay */}
        <div className="absolute inset-0">
          {/* Screen Content */}
          <div
            className="absolute overflow-hidden"
            style={{
              left: '16.25%',
              top: '18.3%',
              width: '67.5%',
              height: '43.3%',
              borderRadius: '2px'
            }}
          >
            {imageUrl ? (
              <div className={`relative h-[800px] w-full transition-transform duration-[15000ms] ${showScrollEffect ? 'group-hover:translate-y-[-60%]' : ''}`}>
                <Image
                  src={imageUrl}
                  alt={imageAlt}
                  fill
                  className="object-cover object-top"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
            ) : (
              <div className="h-full w-full flex items-center justify-center bg-gray-100">
                {children || (
                  <div className="text-gray-500 text-xs">No content</div>
                )}
              </div>
            )}
            {/* Screen reflection */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-black/5 pointer-events-none"></div>
          </div>

          {/* URL Bar Content */}
          <div
            className="absolute flex items-center justify-center text-xs text-gray-300"
            style={{
              left: '27.5%',
              top: '13.3%',
              width: '30%',
              height: '3.3%'
            }}
          >
            <span className="truncate text-[8px]">
              {websiteUrl.replace('https://', '').replace('http://', '')}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
