'use client';

import React from "react";
import { Timeline } from "@/components/ui/timeline";


export function Experience() {
  const cards = [
    {
      id: 1,
      content: (
        <div className="p-4">
          <h3 className="text-xl font-bold text-white mb-4">AWS & Server Specialist</h3>
          <p className="text-gray-200 mb-4">3rdeyesoft (2024 - Present)</p>
          <ul className="space-y-2 text-gray-300">
            <li>✅ Architecting and managing AWS cloud infrastructure</li>
            <li>✅ Implementing serverless solutions using AWS Lambda</li>
            <li>✅ Optimizing cloud costs and performance</li>
            <li>✅ Managing CI/CD pipelines and DevOps practices</li>
          </ul>
        </div>
      ),
      className: "col-span-1",
      thumbnail: "/hero-sections.png",
    },
    {
      id: 2,
      content: (
        <div className="p-4">
          <h3 className="text-xl font-bold text-white mb-4">Full Stack Developer</h3>
          <p className="text-gray-200 mb-4">Harajgroup (2023)</p>
          <ul className="space-y-2 text-gray-300">
            <li>✅ Developed scalable web applications using Next.js and Node.js</li>
            <li> ✅ Managed AWS Infrastructure and system design  </li>
            <li>✅ Reduced API response time by 60%</li>
          </ul>
        </div>
      ),
      className: "col-span-1",
      thumbnail: "/bento-grids.png",
    },
    {
      id: 3,
      content: (
        <div className="p-4">
          <h3 className="text-xl font-bold text-white mb-4">Full Stack Developer</h3>
          <p className="text-gray-200 mb-4">roor agency (2021 - 2023)</p>
          <ul className="space-y-2 text-gray-300">
            <li>✅ Built responsive web interfaces using React</li>
            <li>✅ Developed RESTful APIs using Express.js</li>
            <li>✅ Managed MongoDB databases and implemented data models</li>
            <li>✅ Integrated third-party APIs and payment gateways</li>
            <li>✅ Collaborated in an Agile development environment</li>
          </ul>
        </div>
      ),
      className: "col-span-1",
      thumbnail: "https://assets.aceternity.com/templates/startup-3.webp",
    },
  ];

  return (
    <div className="h-full w-full relative" id="experience">
      <div className="left-0 w-full">
        <Timeline data={cards.map(card => ({
          title: card.content.props.children[0].props.children, // Extract the h3 text
          content: card.content,
          thumbnail: card.thumbnail
        }))} />
        {/* <LayoutGrid cards={cards} /> */}
      </div>
    </div>
  );
}
