'use client';
import { Card } from "@/components/ui/card";
import { motion } from "framer-motion";
import { StarBorder } from "@/components/ui/star-border";
import Image from "next/image";
// import { Spotlight } from "@/components/ui/spotlight";

export function Hero() {
  return (
    <Card className="h-screen bg-black/[0.96] relative overflow-hidden" id="Home">
   
      <div className="flex h-full w-full  lg:m-12 px-12 flex-col lg:flex-row items-center">
        {/* Left content */}
        <div className="flex-1 p-0 sm:px-10 lg:px-16 xl:px-24 py-12 lg:py-0 relative z-10 flex flex-col justify-center max-w-3xl mx-auto lg:mx-0">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-4 text-sm sm:text-base text-indigo-300 font-medium tracking-wider"
          >
            DEVOPS ENGINEER
          </motion.div>
          
          <motion.h1 
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="flex flex-row text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold bg-clip-text text-transparent bg-gradient-to-b from-neutral-50 to-neutral-400 leading-tight"
          >
            I&apos;m Muhammad Usman
          </motion.h1>
         
          {/* Animated paragraph */}
          <motion.p
            className="mt-6 text-sm text-justify sm:text-base md:text-lg text-neutral-300 max-w-xl leading-relaxed"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            Currently working as a DevOps Engineer, I specialize in automating and orchestrating scalable, resilient cloud infrastructures that streamline and accelerate business operations. From building robust CI/CD pipelines to implementing Infrastructure as Code and monitoring solutions, I enable seamless deployments and reliable system performance.
            <span className="ml-2 text-xl">☁️💻</span>
          </motion.p>

          {/* Added StarBorder Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.2, ease: "easeOut", delay: 0.2 }}
            className="mt-8 flex justify-center md:justify-start"
          >
            <StarBorder
              as="a"
              href="#contact"
              color="hsl(var(--primary))"
              className="cursor-pointer hover:scale-105 transition-transform"
            >
              Let&apos;s Connect
            </StarBorder>
          </motion.div>
        </div>

        {/* Right content - Image section with gradient overlay */}
        <motion.div 
          className="flex-1 relative hidden lg:block lg:h-full w-full lg:w-1/2 overflow-hidden"
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1.2 }}
        >
          <div className="absolute sm:hidden lg:block inset-0">
            <Image
              src="/uxs.jpg"
              alt="Muhammad Usman"
              fill
              className="object-cover object-center"
              priority
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 40vw"
              quality={90}
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black via-black/70 to-black/20 lg:from-black lg:via-black/50 lg:to-transparent"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent lg:from-black/80 lg:to-transparent"></div>
          </div>
        </motion.div>
      </div>
    </Card>
  );
}
