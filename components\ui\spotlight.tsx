'use client';
import React, { useRef, useState, useCallback, useEffect } from 'react';

type SpotlightProps = {
  className?: string;
  size?: number;
  cursorSize?: number;
  cursorColor?: string;
};

export function Spotlight({
  className = '',
  size = 150,
  cursorSize = 24,
  cursorColor = 'white',
}: SpotlightProps) {
  const spotlightRef = useRef<HTMLDivElement>(null);
  const cursorRef = useRef<HTMLDivElement>(null);
  const [parentElement, setParentElement] = useState<HTMLElement | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    if (spotlightRef.current) {
      const parent = spotlightRef.current.parentElement;
      if (parent) {
        parent.style.position = 'relative';
        setParentElement(parent);
      }
    }
  }, []);

  const handleMouseMove = useCallback(
    (event: MouseEvent) => {
      if (!parentElement) return;
      const { left, top } = parentElement.getBoundingClientRect();
      
      requestAnimationFrame(() => {
        setMousePosition({
          x: event.clientX - left,
          y: event.clientY - top
        });
      });
    },
    [parentElement]
  );

  useEffect(() => {
    if (!parentElement) return;

    parentElement.addEventListener('mousemove', handleMouseMove, { passive: true });
    
    // Add custom cursor style
    parentElement.style.cursor = 'none';

    return () => {
      parentElement.removeEventListener('mousemove', handleMouseMove);
      
      // Reset cursor style
      parentElement.style.cursor = '';
    };
  }, [parentElement, handleMouseMove]);

  useEffect(() => {
    const spotlight = spotlightRef.current;
    const cursor = cursorRef.current;
    
    if (spotlight && cursor) {
      spotlight.style.left = `${mousePosition.x - size / 2}px`;
      spotlight.style.top = `${mousePosition.y - size / 2}px`;
      
      cursor.style.left = `${mousePosition.x - cursorSize / 2}px`;
      cursor.style.top = `${mousePosition.y - cursorSize / 2}px`;
    }
  }, [mousePosition, size, cursorSize]);

  const spotlightClasses = `pointer-events-none absolute rounded-full bg-[radial-gradient(circle_at_center,var(--tw-gradient-stops),transparent_80%)] blur-xl from-indigo-300 via-purple-200 to-sky-200 ${className}`;

  return (
    <>
      <div
        ref={spotlightRef}
        className={spotlightClasses}
        style={{
          width: size,
          height: size,
          willChange: 'transform',
          opacity: 1,
        }}
      />
      <div
        ref={cursorRef}
        className="pointer-events-none absolute z-50"
        style={{
          width: `${cursorSize}px`,
          height: `${cursorSize}px`,
          borderRadius: '50%',
          border: `1.5px solid ${cursorColor}`,
          backgroundColor: 'transparent',
          willChange: 'transform',
        }}
      />
    </>
  );
}
