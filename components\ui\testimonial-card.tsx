// import { cn } from "@/lib/utils"
// import Image from "next/image"
// import Link from "next/link"

// export interface TestimonialAuthor {
//   name: string
//   role: string
//   avatar: string
//   company?: {
//     name: string
//     logo?: string
//   }
// }

// interface TestimonialCardProps {
//   author: TestimonialAuthor
//   text: string
//   href?: string
// }

// export function TestimonialCard({ author, text, href }: TestimonialCardProps) {
//   // If there's no href, use a div instead of Link
//   if (!href) {
//     return (
//       <div
//         className={cn(
//           "group relative flex w-[320px] shrink-0 flex-col justify-between rounded-2xl border p-6 backdrop-blur",
//           "bg-background/50 hover:bg-accent/50",
//           "transition duration-300"
//         )}
//       >
//         <CardContent author={author} text={text} />
//       </div>
//     );
//   }

//   // If there is an href, use Link
//   return (
//     <Link
//       href={href}
//       className={cn(
//         "group relative flex w-[320px] shrink-0 flex-col justify-between rounded-2xl border p-6 backdrop-blur",
//         "bg-background/50 hover:bg-accent/50",
//         "transition duration-300",
//         "cursor-pointer"
//       )}
//     >
//       <CardContent author={author} text={text} />
//     </Link>
//   );
// }

// // Separate the content into its own component to avoid duplication
// function CardContent({ author, text }: { author: TestimonialAuthor; text: string }) {
//   return (
//     <div className="relative z-10">
//       <p className="mb-8 text-base leading-relaxed text-muted-foreground">
//         "{text}"
//       </p>
//       <div className="flex items-center gap-4">
//         <Image
//           src={author.avatar}
//           alt={author.name}
//           width={48}
//           height={48}
//           className="rounded-full"
//         />
//         <div>
//           <p className="text-sm font-semibold">{author.name}</p>
//           <p className="text-sm text-muted-foreground">
//             {author.role}
//             {author.company && (
//               <>
//                 {" @ "}
//                 <span className="font-medium">{author.company.name}</span>
//               </>
//             )}
//           </p>
//         </div>
//       </div>
//     </div>
//   );
// }